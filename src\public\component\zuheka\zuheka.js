"use strict";
Vue.component("app-zuheka", function (resolve, reject) {
  $.get("component/zuheka/zuheka.html").then(function (res) {
    resolve({
      template: res,
      props: {
        login: {
          type: Object,
        },
        handle_foucs_input: {
          type: Boolean,
          value: false,
        },
        "transition-direction": {
          type: String,
          value: "slide-down",
        },
      },
      data: function () {
        let goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
        let serverLimit = 999;
        if (goodsSearchConfig) {
          goodsSearchConfig = JSON.parse(goodsSearchConfig);
          serverLimit = goodsSearchConfig["serverLimit"] || 10;
        }
        return {
          //订单号
          orderNo: "",
          //充次卡下单完智慧跳转支付页面
          isRechargeCard: false,
          //收银台--充次卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
          isPayStatus: 4,
          //收银台到支付页面的标志位
          billToPay: 0,
          buy_receipt: false,
          //下单时候需要的充次卡信息
          extraData: {},
          //loading页面转圈圈
          loading: false,
          url: baseUrl,
          //充次卡开单详情
          zhk_server_details_name: [],
          //循环的服务列表
          zhk_server_name: [],
          //搜索的关键字
          search_keyword: "",
          //备注信息
          beizhu_info: "",
          //最后需要提交支付的值
          pay_all: 0,
          pay_all_show: "0.00",
          loginInfo: {}, // 登录信息
          serverLabelid: "",
          serverPage: 1, //服务查询的页数
          serverLimit: serverLimit, //服务查询的个数
          isServerScroll: false,
          serverAllCount: 0,
          busy: false,
          loadingtip: "加载中···",
          memberInfo: {},
          //判断是不是展示销售
          zhk_xiao_shou: false,
          //存贮销售内容
          zhkxiaoshous: [],
          //销售用来存储以选择的销售信息。
          xiao_shou_zhanshi: [],
          //在页面展示选中的销售内容
          zhk_xiao_shou_zhanshi: [],
          //页面展示销售的变量
          SalesShow: "",
          selesShows: "请选择",
          giftPickTimeOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
            },
          },
          // 选择协助员工
          helpStaffArr: {},
          helpStaffAll: [],
          helpStaffVisible: false,
          checkHelpStaffArr: [],
          bindStaffId: 0,
          isactive1: 4,
          // 有效期等
          cardinfo: {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          },
          // 左键长按相关变量
          longPressTimer: null,
          longPressInterval: null,
          longPressFirstTimer: null,
          isLongPressing: false,
          wasLongPressed: false,
          leftPressData: null,
          leftPressEvent: null,
          leftPressStartTime: 0,
          isLeftPressed: false,
          // 右键长按相关变量
          rightLongPressTimer: null,
          rightLongPressInterval: null,
          rightLongPressFirstTimer: null,
          isRightLongPressing: false,
          wasRightLongPressed: false,
          rightLongPressData: null,
          rightLongPressEvent: null,
          rightPressStartTime: 0,
          isRightPressed: false,
        };
      },
      mounted: function () {
        this.serviceList();
        this.setupPriceTagHover();
        this.setupGlobalMouseEvents();
      },

      methods: {
        setupPriceTagHover: function () {
          // 使用事件委托来处理动态添加的元素
          this.$nextTick(function () {
            $(document).on("mouseenter", ".o-price-select-tag", function () {
              $(this).closest(".o-service-card").addClass("price-tag-hovered");
            });
            $(document).on("mouseleave", ".o-price-select-tag", function () {
              $(this)
                .closest(".o-service-card")
                .removeClass("price-tag-hovered");
            });
          });
        },
        setupGlobalMouseEvents: function () {
          const _self = this;
          // 全局鼠标松开事件，确保长按能正确结束
          _self.globalMouseUpHandler = function (event) {
            if (event.button === 0) {
              _self.handleGlobalLeftMouseUp(event);
            } else if (event.button === 2) {
              _self.handleGlobalRightMouseUp(event);
            }
          };
          document.addEventListener("mouseup", _self.globalMouseUpHandler);
        },
        handleMemberSelect: function (memberInfo) {
          this.memberInfo = memberInfo;
        },
        //调整单价
        handleUnitPriceChange: function (index) {
          const _self = this;
          const item = _self.zhk_server_details_name[index];
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          _self.CalculatePrice();
        },

        //设置单价
        handleSetUnitPrice: function (index, price) {
          const item = this.zhk_server_details_name[index];
          const unitPrice = parseFloat(price) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 设置单价
          item.unitPrice = unitPrice.toFixed(2);

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          this.CalculatePrice();

          // 触发单价输入框动画效果
          this.$nextTick(() => {
            const serviceCard =
              document.querySelectorAll(".o-service-card")[index];
            if (serviceCard) {
              const priceInput = serviceCard.querySelector(
                ".f-price-select-hover"
              );
              if (priceInput) {
                priceInput.classList.add("price-update-animation");
                setTimeout(() => {
                  priceInput.classList.remove("price-update-animation");
                }, 600);
              }
            }
          });
        },
        closeHelpStaffVisible(type) {
          // 1, 确定关闭 2，取消关闭
          if (type) {
            this.helpStaffArr[this.isactive1] = [...this.checkHelpStaffArr];
          }
          this.checkHelpStaffArr = [];
          this.helpStaffVisible = false;
        },
        //聚焦
        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
            }.bind(this)
          );
        },
        //clearPage 清空页面
        clearPage: function () {
          this.zhk_server_details_name = [];
          this.beizhu_info = "";
          this.zhk_xiao_shou_zhanshi = [];
          this.xiao_shou_zhanshi = [];
          this.SalesShow = "";
          this.pay_all_show = "0.00";
          this.pay_all = 0;
          this.helpStaffArr[this.isactive1] = [];
          this.cardinfo = {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          };
          this.memberInfo = {};

          // 重置服务卡片的动画状态
          this.zhk_server_name.forEach((item) => {
            this.$set(item, "_justAdded", false);
          });

          this.inputFocus(this.$refs.search_keyword);
        },
        //关闭收款
        bindClosePay: function (flag) {
          this.buy_receipt = flag;
          // 调用 member-search-bar 组件的 clearMemberInfo 方法
          if (this.$refs.memberSearchBar) {
            this.$refs.memberSearchBar.clearMemberInfo();
          }
          // this.clearPage();
        },
        //选择销售弹框
        zhkchange_xiaoshou: function () {
          this.zhk_xiao_shou = true;
          this.loading = true;
          let salemenArr = this.zhk_xiao_shou_zhanshi;
          this.xiao_shou_zhanshi = salemenArr;
          $.ajax({
            url: this.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: this.loginInfo.merchantid,
              storeid: this.loginInfo.storeid,
            },
            success: (res) => {
              if (res.code == 1) {
                this.zhkxiaoshous = res.data;
                if (salemenArr?.length != 0) {
                  for (let i = 0; i < this.zhkxiaoshous.length; i++) {
                    let flag = true;
                    for (let j = 0; j < salemenArr.length; j++) {
                      if (salemenArr[j].id == this.zhkxiaoshous[i].id) {
                        this.zhkxiaoshous[i]["is_choice_xiaoshou"] = true;
                        flag = false;
                        break;
                      }
                    }
                    if (flag) {
                      this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                    }
                  }
                } else {
                  for (let i = 0; i < this.zhkxiaoshous.length; i++) {
                    this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                  }
                }
                this.loading = false;
              } else {
                this.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        //充次卡确定销售
        zhk_xiaoshou_save: function () {
          this.SalesShow = "";
          if (this.xiao_shou_zhanshi) {
            this.zhk_xiao_shou_zhanshi = JSON.parse(
              JSON.stringify(this.xiao_shou_zhanshi)
            );
            var arrlength = this.zhk_xiao_shou_zhanshi.length;
            if (this.zhk_xiao_shou_zhanshi) {
              for (let i = 0; i < arrlength; i++) {
                if (i == arrlength - 1) {
                  this.SalesShow += this.zhk_xiao_shou_zhanshi[i]["nickname"];
                } else {
                  this.SalesShow +=
                    this.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
                }
              }
            } else {
              this.SalesShow = "";
            }
          } else {
            this.zhk_xiao_shou_zhanshi = this.xiao_shou_zhanshi;
            this.SalesShow = "";
          }
          this.xiao_shou_zhanshi = [];
          this.zhk_xiao_shou = false;
        },
        //充次卡取消销售
        zhk_xiaoshou_over: function () {
          this.SalesShow = "";
          var arrlength = this.zhk_xiao_shou_zhanshi.length;
          if (this.zhk_xiao_shou_zhanshi) {
            for (let i = 0; i < arrlength; i++) {
              if (i == arrlength - 1) {
                this.SalesShow += this.zhk_xiao_shou_zhanshi[i]["nickname"];
              } else {
                this.SalesShow +=
                  this.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
              }
            }
          } else {
            this.SalesShow = "";
          }
          for (let i = 0; i < this.zhkxiaoshous.length; i++) {
            this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
          }
          this.$forceUpdate();
          this.zhk_xiao_shou = false;
        },
        //选择销售框
        chioce_xiaoshou: function (index, data, xiao_shou_id) {
          let item = this.zhkxiaoshous[index];
          if (data == true) {
            item["is_choice_xiaoshou"] = data;
            this.xiao_shou_zhanshi.push(item);
          } else {
            for (let j = 0; j < this.xiao_shou_zhanshi.length; j++) {
              if (this.xiao_shou_zhanshi[j]["id"] == xiao_shou_id) {
                this.xiao_shou_zhanshi.splice(j, 1);
              }
            }
          }
          this.$forceUpdate();
        },

        //删除服务
        zhk_open_details_price_del: function (index) {
          // 获取要删除的服务信息用于提示
          var deletedService = this.zhk_server_details_name[index];

          // 添加删除动画类
          this.$set(deletedService, "_removing", true);

          // 延迟删除以显示缩小动画
          setTimeout(() => {
            // 删除服务项
            this.zhk_server_details_name.splice(index, 1);
            this.CalculatePrice();
          }, 100);
        },

        //自动查询服务
        serviceList: function (flag) {
          this.loading = true;
          $.ajax({
            url: this.url + "/android/Service/serviceList",
            type: "post",
            data: {
              keyword: this.search_keyword,
              labelid: this.serverLabelid,
              sort: 1, // 默认正序，1正序 2倒序
              status: 1, // 1上架。  2下架
              storeid: this.loginInfo.storeid,
              page: this.serverPage,
              limit: this.serverLimit,
            },
            success: (res) => {
              this.loading = false;
              if (res.code == 0) {
                this.serverAllCount = res.count;
                if (this.serverAllCount < 10) {
                  this.busy = false;
                }
                if (flag == 1) {
                  this.zhk_server_name = this.zhk_server_name.concat(res.data);
                  if (res.count == 0) {
                    this.isServerScroll = true;
                  } else {
                    this.isServerScroll = false;
                  }
                } else {
                  this.zhk_server_name = res.data;
                  this.isServerScroll = false;
                }
                //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
                //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
                for (let i = 0; i < this.zhk_server_name.length; i++) {
                  this.zhk_server_name[i]["technician_id"] = [];
                  this.zhk_server_name[i]["salesmen"] = [];
                  this.zhk_server_name[i]["num"] = 1;
                  this.zhk_server_name[i]["manualDiscount"] = 1;
                  this.zhk_server_name[i]["discount"] = 1;
                  this.zhk_server_name[i]["manualDiscountCard"] = {};
                  this.zhk_server_name[i]["zhonglei"] = 1; //添加一个字段用来判断种类
                  this.zhk_server_name[i]["_animationId"] = Date.now() + i; //添加动画标识
                }
              } else {
                this.zhk_server_name = [];
                this.$message({
                  type: "error",
                  message: "暂无数据",
                  duration: 1500,
                });
              }
            },
            error: () => {
              this.loading = false;
            },
          });
        },

        // 开始连续添加
        startContinuousAdd: function () {
          if (!this.isLongPressing || !this.isLeftPressed) return;

          // 第一次+10在200ms后执行
          this.longPressFirstTimer = setTimeout(() => {
            if (!this.isLongPressing || !this.isLeftPressed) return;

            // 第一次添加10个数量
            this.addServiceQuantity(
              this.leftPressData,
              this.leftPressEvent,
              10
            );

            // 后续每1000ms执行一次
            this.longPressInterval = setInterval(() => {
              if (!this.isLongPressing || !this.isLeftPressed) {
                clearInterval(this.longPressInterval);
                return;
              }

              // 添加10个数量
              this.addServiceQuantity(
                this.leftPressData,
                this.leftPressEvent,
                10
              );
            }, 1000);
          }, 200);
        },
        // 添加服务数量（支持指定数量）
        addServiceQuantity: function (value, event, quantity = 1) {
          const data = value;
          var zhk_price;
          var priceIndex = data["price"].indexOf("-");
          if (priceIndex == -1) {
            zhk_price = data["price"];
          } else {
            zhk_price = data["price"].slice(priceIndex + 1);
          }

          // 查找是否已存在该服务
          let duplicateIndex = -1;
          for (let i = 0; i < this.zhk_server_details_name.length; i++) {
            if (data["id"] == this.zhk_server_details_name[i]["id"]) {
              duplicateIndex = i;
              break;
            }
          }

          if (duplicateIndex !== -1) {
            // 如果服务已存在，增加指定数量
            const item = this.zhk_server_details_name[duplicateIndex];
            const newQuantity = Math.min(item.numberAvailable + quantity, 9999);
            const actualAdded = newQuantity - item.numberAvailable;

            if (actualAdded > 0) {
              item.numberAvailable = newQuantity;

              // 重新计算价格
              const unitPrice = parseFloat(item.unitPrice) || 0;
              const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
              item.subtotal = subtotal;
              item.totalAmount = Math.round(
                unitPrice * item.numberAvailable * 100
              );

              // 显示弹出数字效果
              if (event) {
                this.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  newQuantity,
                  quantity >= 10,
                  false
                );
              }

              // 滚动到对应项并高亮
              this.$nextTick(() => {
                const serviceCards =
                  document.querySelectorAll(".o-service-card");
                if (serviceCards[duplicateIndex]) {
                  serviceCards[duplicateIndex].scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });

                  serviceCards[duplicateIndex].classList.add(
                    "o-service-card-highlight"
                  );
                  setTimeout(() => {
                    serviceCards[duplicateIndex].classList.remove(
                      "o-service-card-highlight"
                    );
                  }, 1000);
                }
              });

              this.CalculatePrice();
            }
          } else {
            // 如果服务不存在，添加新服务
            data["numberAvailable"] = quantity;
            data["zhk_price_show"] = zhk_price;
            data["zhk_price"] = zhk_price * 100;
            data["unitPrice"] = data["price"];
            data["totalAmount"] = zhk_price * quantity * 100;
            data["subtotal"] = (zhk_price * quantity).toFixed(2);
            data["_animationId"] = Date.now() + Math.random();

            this.$set(value, "_justAdded", true);
            setTimeout(() => {
              this.$set(value, "_justAdded", false);
            }, 600);

            this.zhk_server_details_name.unshift(
              JSON.parse(JSON.stringify(data))
            );

            // 显示弹出数字效果
            if (event) {
              this.createPopupNumber(
                event.clientX,
                event.clientY,
                quantity,
                quantity >= 10,
                false
              );
            }

            // 滚动到顶端
            this.$nextTick(() => {
              const serviceCardsContainers =
                document.querySelectorAll(".o-scrollbar");
              const rightScrollContainer = serviceCardsContainers[1];
              if (rightScrollContainer) {
                rightScrollContainer.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }
            });

            this.CalculatePrice();
          }
        },
        // 统一的鼠标按下处理
        handleMouseDown: function (value, event) {
          event.preventDefault();
          event.stopPropagation();

          if (event.button === 0) {
            // 左键按下
            this.handleLeftMouseDown(value, event);
          } else if (event.button === 2) {
            // 右键按下
            this.handleRightMouseDown(value, event);
          }
        },

        // 左键按下处理
        handleLeftMouseDown: function (value, event) {
          this.leftPressStartTime = performance.now();
          this.leftPressData = value;
          this.leftPressEvent = event;
          this.isLeftPressed = true;
          this.isLongPressing = false;
          this.wasLongPressed = false;

          // 400ms后开始长按模式
          this.longPressTimer = setTimeout(() => {
            if (this.isLeftPressed) {
              this.isLongPressing = true;
              this.startContinuousAdd();
            }
          }, 400);
        },

        // 右键按下处理
        handleRightMouseDown: function (value, event) {
          this.rightPressStartTime = performance.now();
          this.rightLongPressData = value;
          this.rightLongPressEvent = event;
          this.isRightPressed = true;
          this.isRightLongPressing = false;
          this.wasRightLongPressed = false;

          // 400ms后开始右键长按模式
          this.rightLongPressTimer = setTimeout(() => {
            if (this.isRightPressed) {
              this.isRightLongPressing = true;
              this.startRightContinuousRemove();
            }
          }, 400);
        },

        // 统一的鼠标松开处理
        handleMouseUp: function (event) {
          if (event.button === 0) {
            this.handleLeftMouseUp(event);
          } else if (event.button === 2) {
            this.handleRightMouseUp(event);
          }
        },

        // 左键松开处理
        handleLeftMouseUp: function (event) {
          if (!this.isLeftPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.leftPressStartTime || 0);
          const wasInLongPressMode = this.isLongPressing;

          // 清理状态和定时器
          this.isLeftPressed = false;
          this.clearLeftPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.addServiceQuantity(this.leftPressData, this.leftPressEvent, 1);
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasLongPressed = true;
            setTimeout(() => {
              this.wasLongPressed = false;
            }, 100);
          }

          this.isLongPressing = false;
        },

        // 右键松开处理
        handleRightMouseUp: function (event) {
          if (!this.isRightPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.rightPressStartTime || 0);
          const wasInLongPressMode = this.isRightLongPressing;

          // 清理状态和定时器
          this.isRightPressed = false;
          this.clearRightLongPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.removeServiceQuantity(
              this.rightLongPressData,
              this.rightLongPressEvent,
              1
            );
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasRightLongPressed = true;
            setTimeout(() => {
              this.wasRightLongPressed = false;
            }, 100);
          }

          this.isRightLongPressing = false;
        },

        // 全局左键松开处理
        handleGlobalLeftMouseUp: function (event) {
          if (this.isLeftPressed || this.isLongPressing) {
            this.handleLeftMouseUp(event);
          }
        },

        // 全局右键松开处理
        handleGlobalRightMouseUp: function (event) {
          if (this.isRightPressed || this.isRightLongPressing) {
            this.handleRightMouseUp(event);
          }
        },

        // 清理左键相关定时器
        clearLeftPressTimers: function () {
          if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
          }
          if (this.longPressFirstTimer) {
            clearTimeout(this.longPressFirstTimer);
            this.longPressFirstTimer = null;
          }
          if (this.longPressInterval) {
            clearInterval(this.longPressInterval);
            this.longPressInterval = null;
          }
        },

        // 清除右键长按定时器
        clearRightLongPressTimers: function () {
          if (this.rightLongPressTimer) {
            clearTimeout(this.rightLongPressTimer);
            this.rightLongPressTimer = null;
          }
          if (this.rightLongPressFirstTimer) {
            clearTimeout(this.rightLongPressFirstTimer);
            this.rightLongPressFirstTimer = null;
          }
          if (this.rightLongPressInterval) {
            clearInterval(this.rightLongPressInterval);
            this.rightLongPressInterval = null;
          }
        },

        // 处理右键点击
        handleRightClick: function (value, event) {
          // 阻止右键菜单，实际逻辑在mousedown/mouseup中处理
          event.preventDefault();
          event.stopPropagation();
          return false;
        },
        // 开始右键连续减少
        startRightContinuousRemove: function () {
          if (!this.isRightLongPressing || !this.isRightPressed) return;

          // 第一次-10在200ms后执行
          this.rightLongPressFirstTimer = setTimeout(() => {
            if (!this.isRightLongPressing || !this.isRightPressed) return;

            // 第一次减少10个数量
            this.removeServiceQuantity(
              this.rightLongPressData,
              this.rightLongPressEvent,
              10
            );

            // 后续每1000ms执行一次
            this.rightLongPressInterval = setInterval(() => {
              if (!this.isRightLongPressing || !this.isRightPressed) {
                clearInterval(this.rightLongPressInterval);
                return;
              }

              // 减少10个数量
              this.removeServiceQuantity(
                this.rightLongPressData,
                this.rightLongPressEvent,
                10
              );
            }, 1000);
          }, 200);
        },
        // 减少服务数量（支持指定数量）
        removeServiceQuantity: function (value, event, quantity = 1) {
          const data = value;

          // 查找是否存在该服务
          let existingIndex = -1;
          for (let i = 0; i < this.zhk_server_details_name.length; i++) {
            if (data["id"] == this.zhk_server_details_name[i]["id"]) {
              existingIndex = i;
              break;
            }
          }

          if (existingIndex !== -1) {
            const item = this.zhk_server_details_name[existingIndex];
            const newQuantity = Math.max(item.numberAvailable - quantity, 0);
            const actualRemoved = item.numberAvailable - newQuantity;

            if (actualRemoved > 0) {
              if (newQuantity === 0) {
                // 如果数量变为0，删除该服务项
                this.$set(item, "_removing", true);
                setTimeout(() => {
                  this.zhk_server_details_name.splice(existingIndex, 1);
                  this.CalculatePrice();
                }, 100);
              } else {
                // 更新数量
                item.numberAvailable = newQuantity;

                // 重新计算价格
                const unitPrice = parseFloat(item.unitPrice) || 0;
                const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
                item.subtotal = subtotal;
                item.totalAmount = Math.round(
                  unitPrice * item.numberAvailable * 100
                );

                this.CalculatePrice();
              }

              // 显示弹出数字效果（负数）
              if (event) {
                this.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  newQuantity,
                  quantity >= 10,
                  true
                );
              }

              // 滚动到对应项并高亮
              if (newQuantity > 0) {
                this.$nextTick(() => {
                  const serviceCards =
                    document.querySelectorAll(".o-service-card");
                  if (serviceCards[existingIndex]) {
                    serviceCards[existingIndex].scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                    });

                    serviceCards[existingIndex].classList.add(
                      "o-service-card-highlight"
                    );
                    setTimeout(() => {
                      serviceCards[existingIndex].classList.remove(
                        "o-service-card-highlight"
                      );
                    }, 1000);
                  }
                });
              }
            }
          }
        },
        //
        /**
         * 创建弹出数字效果
         * @param {*} x 鼠标位置
         * @param {*} y 鼠标位置
         * @param {*} number 显示的内容
         * @param {*} isLongPress 是否长按
         * @param {*} isDecrease 是否减少
         */
        createPopupNumber: function (
          x,
          y,
          number,
          isLongPress = false,
          isDecrease = false
        ) {
          const popup = document.createElement("div");
          const popupAdd = document.createElement("div");

          // 根据操作类型和模式设置样式类
          let className = "number-popup ";
          let classNameAdd = "number-popup-add ";
          // 单击用蓝色，长按用紫色
          className += isLongPress
            ? "number-popup-purple"
            : "number-popup-blue";

          if (isDecrease) {
            // 减少操作：单击用蓝色，长按用红色
            classNameAdd += "number-popup-red";
            popupAdd.textContent = isLongPress ? "-10" : "-1";
          } else {
            // 增加操作：单击用蓝色，长按用红色
            classNameAdd += isLongPress
              ? "number-popup-purple"
              : "number-popup-blue";
            popupAdd.textContent = isLongPress ? "+10" : "+1";
          }

          popup.className = className;
          popupAdd.className = classNameAdd;
          popup.textContent = number.toString();
          popup.style.left = x + "px";
          popupAdd.style.left = x + "px";
          popup.style.top = y + "px";
          popupAdd.style.top = y + "px";

          document.body.appendChild(popup);
          document.body.appendChild(popupAdd);

          // 动画结束后移除元素
          setTimeout(() => {
            if (popup.parentNode) {
              popup.parentNode.removeChild(popup);
            }
            if (popupAdd.parentNode) {
              popupAdd.parentNode.removeChild(popupAdd);
            }
          }, 1000);
        },
        //充次卡点击服务列表 - 现在主要用于兼容性，实际逻辑在mousedown/mouseup中处理
        bind_zhk_add_server: function (value, event) {
          // 如果刚刚结束长按，阻止click事件
          if (this.wasLongPressed || this.wasRightLongPressed) {
            event.preventDefault();
            event.stopPropagation();
            return;
          }

          // 其他情况下，click事件不做任何处理，因为逻辑已经在mouseup中处理了
          event.preventDefault();
          event.stopPropagation();
        },
        //每次添加服务和删除服务都会触发价格的事件
        CalculatePrice: function () {
          this.pay_all = 0;
          if (this.zhk_server_details_name.length == 0) {
            this.pay_all_show = "0.00";
          } else {
            for (let i = 0; i < this.zhk_server_details_name.length; i++) {
              this.pay_all += this.zhk_server_details_name[i]["totalAmount"];
            }
            this.pay_all_show = (this.pay_all / 100).toFixed(2);
          }
        },
        // 键盘等搜索按钮搜索事件
        billingInquiryEnter: function () {
          this.serverPage = 1;
          this.serviceList();
        },
        loadMoreProduct: function () {
          this.isServerScroll = true;
          if (this.serverAllCount == this.zhk_server_name.length) {
            this.loadingtip = "数据已全部加载";
            return false;
          } else {
            this.busy = true;
            this.loadingtip = "加载中···";
            this.serverPage++;
            this.serviceList(1);
          }
        },
        //减少充次卡的次数
        jianshao: function (index) {
          const item = this.zhk_server_details_name[index];

          if (item.numberAvailable > 1) {
            item.numberAvailable--;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          this.CalculatePrice();
        },
        //增加充次卡的次数
        zengjia: function (index) {
          const item = this.zhk_server_details_name[index];

          if (item.numberAvailable < 9999) {
            item.numberAvailable++;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          this.CalculatePrice();
        },
        handleNumInputChange: function (e, index) {
          const item = this.zhk_server_details_name[index];
          const numValue = parseInt(e.target.value);

          // 验证输入值是否为有效数字
          if (isNaN(numValue)) {
            // 如果输入无效，重置为1
            item.numberAvailable = 1;
          } else {
            // 限制数量范围在1-9999之间
            if (numValue < 1) {
              item.numberAvailable = 1;
            } else if (numValue > 9999) {
              item.numberAvailable = 9999;
            } else {
              item.numberAvailable = numValue;
            }
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          // 重新计算总价
          this.CalculatePrice();
        },

        //调整小计
        handleSubtotalChange: function (index) {
          const item = this.zhk_server_details_name[index];
          const subtotal = parseFloat(item.subtotal) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 根据小计和数量计算单价 = 小计 / 数量
          const unitPrice =
            quantity > 0 ? (subtotal / quantity).toFixed(2) : "0.00";
          item.unitPrice = unitPrice;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(subtotal * 100);

          // 重新计算总价
          this.CalculatePrice();
        },

        //充次卡收款
        goSettle: function (flag) {
          var saveorderData = this.zhk_server_details_name;
          //赋值购买会员的信息
          var buyerId = this.memberInfo.id;
          var orderItems = [];

          //获取销售id
          var salesid = this.zhk_xiao_shou_zhanshi.map((item) => item["id"]);

          for (var i = 0; i < saveorderData.length; i++) {
            orderItems.push({
              // cardName: save_orderData[i].manualDiscountCard['cardName'] || '',
              // cardName: '',
              // cardDetailsId: save_orderData[i].manualDiscountCard['id'] || 0,     // (开单使用)使用卡项详情的id
              cardDetailsId: 0, // 【可选】(开单使用)使用卡项详情的id
              // cardId: save_orderData[i].manualDiscountCard['membercard_id'] || 0,   // 卡项id
              cardId: 0, // 【可选】卡项id
              // discount: save_orderData[i].manualDiscountCard['discount'] || '10', // 折扣（开单选择充值卡有）
              discount: 1, // 【可选】折扣（开单选择充值卡有）
              equityType: 1, // 【可选】1 无权益 2折扣 3抵扣 4手动改价
              goodsId: saveorderData[i].id, // 【必填】商品id 服务，产品卡项都填写id
              itemId: 0, // 【可选】
              itemImgId: saveorderData[i].itemImgId || "0", // 【可选】预览图id
              itemName: saveorderData[i].service_name, // 【必填】商品名称
              itemType: 1, // 【必填】1 服务 2产品 3卡项 4充值
              num: saveorderData[i].numberAvailable, // 【必填】数量
              originPrice: saveorderData[i].unitPrice * 100, // 【必填】单价  原价( 分 )
              average_price: saveorderData[i].unitPrice * 100, // 【必填】新单价  原价( 分 )
              recharge_money: 0, // 【条件必填】充值金额（本金）金额 （分） itemType=4时必传
              realPay: saveorderData[i].subtotal * 100, // 【必填】小计（分）
              present_money: 0, // 【条件必填】充值（赠送）金额 (分) itemType=4时必传
              salesmen: salesid || [], // 【可选】选择的销售id
              skuId: 0, // 【可选】规格id，非规格天写0
              skuName: "", // 【可选】规格名称（如：红色,大）没有填空字符串
              stage: "1", // 【可选】当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
              technicians: [], // 【可选】服务人员id
            });
          }

          //充次卡收款需要判断是不是要加会员
          if (!this.memberInfo.id) {
            this.$message({
              type: "warning ",
              message: "请选择会员",
              duration: 1500,
            });
            return;
          }

          if (!saveorderData.length) {
            this.$message({
              type: "warning ",
              message: "请选择服务",
              duration: 2500,
            });
            return;
          }

          this.extraData = {};
          if (this.cardinfo.permanent == 1) {
            this.extraData["indate"] = 0;
          }

          if (this.cardinfo.permanent == 2) {
            if (!this.cardinfo.validity_time) {
              this.$message({
                type: "warning ",
                message: "请输入有效天数",
                duration: 2500,
              });
              return;
            }
            this.extraData["indate"] = 0;
            this.extraData["effectiveMethod"] = this.cardinfo.effectiveMethod;
            this.extraData["validity_time"] = this.cardinfo.validity_time;
          }

          if (this.cardinfo.permanent == 3) {
            if (!this.cardinfo.zuheka_validity) {
              this.$message({
                type: "warning ",
                message: "请选择到期日期",
                duration: 2500,
              });
              return;
            }
            this.extraData["indate"] = this.cardinfo.zuheka_validity;
          }

          this.extraData["comboCardMoney"] = parseInt(
            Number(this.pay_all_show) * 100
          );

          if (this.helpStaffArr[this.isactive1]) {
            this.extraData["help_staff"] = this.helpStaffArr[
              this.isactive1
            ].map((item) => item["id"]);
          }

          $.ajax({
            url: this.url + "/android/order/orderSave",
            type: "post",
            data: {
              // addressInfo: "", // 【可选】收货地址信息
              // bookerid: 0, // 【可选】预约id 来源是预约时使用
              buyerId: buyerId, // 【必填】用户id (orderType为3,4,5,7,8时必填)
              cashierId: this.loginInfo.id, // 【必填】收银员id
              // dispatchFee: 0, // 【可选】运费 （分）
              // dispatchType: 0, // 【可选】配送类型： 1，到店自提，2，配送，0，非配送
              merchantid: this.loginInfo.merchantid, // 【必填】商户id
              orderGiftItems: [], // 【可选】订单礼品数组（预留字段）
              orderItems: JSON.stringify(orderItems), // 【必填】订单的详情信息
              // orderNo: 0, // 【可选】订单号（取单时需要传）
              orderType: 5, // 【必填】1：服务；2：产品；3：套餐卡；4：充值 5：充次卡；6 直接收款
              promotions: [], // 【可选】预留字段（优惠信息）
              // presentData: JSON.stringify([]), // 【可选】预留字段（优惠信息）
              remark: this.beizhu_info, // 【可选】订单备注
              sourceType: 1, // 【必填】来源类型 1,开单，2,预约，3,取单
              storeid: this.loginInfo.storeid, // 【必填】店铺id
              totalPay: Math.round(this.pay_all_show * 100), // 【可选，后台用于打印日志看】订单总价（分）
              shift_no: this.loginInfo.shift_no, // 【必填】班次单号 (android接口必填)
              extraData: JSON.stringify(this.extraData), // 【可选】充次卡额外参数
            },
            success: (res) => {
              this.loading = false;
              if (res.code != 1) {
                this.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
                return;
              }

              this.helpStaffArr[this.isactive1] = [];
              if (flag == "receipt") {
                //收款
                this.buy_receipt = true;
                this.loading = false;
                this.orderNo = res.data.orderNo;
                this.orderId = res.data.id;
                this.isRechargeCard = true;
                this.zhk_server_details_name = [];
                this.billToPay = 1;
                this.isPayStatus = 0; // 开启会员支付
              }
            },
            error: () => {
              this.loading = false;
            },
          });
        },
      },
      filters: {
        ellipsis(value) {
          if (!value) return "";
          if (value.length > 12) {
            return value.slice(0, 12) + "...";
          }
          return value;
        },

        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        login: {
          handler(n) {
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
        search_keyword: {
          handler(n) {
            this.serviceList();
          },
          deep: true,
          immediate: true,
        },
      },
      beforeDestroy: function () {
        // 清理事件监听器
        $(document).off("mouseenter", ".o-price-select-tag");
        $(document).off("mouseleave", ".o-price-select-tag");

        // 清理长按定时器
        this.clearLeftPressTimers();
        this.clearRightLongPressTimers();

        // 清理全局事件监听器
        if (this.globalMouseUpHandler) {
          document.removeEventListener("mouseup", this.globalMouseUpHandler);
        }
      },
    });
  });
});
