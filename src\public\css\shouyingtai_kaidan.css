div {
  -moz-user-select: none; /*mozilar*/
  -webkit-user-select: none; /*webkit*/
  -ms-user-select: none; /*IE*/
  user-select: none;
}

/*暂不用功能隐藏样式*/
.visibility_class {
  visibility: hidden !important;
}

.menu {
  display: flex;
  background-color: #ebeef7;
  height: 60px;
}

.menu_font {
  display: flex;
  margin-left: 100px;
  width: 80%;
}

.menu_font .menu_font_nav {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 20%;
  min-width: 120px;
  height: 100%;
  color: #ffffff;
  font-size: 18px;
}

.cashier {
  display: flex;
  justify-content: space-around;
  opacity: 0.95;
  background: #2b282c;
  padding: 0% 10% 0% 10%;
  height: 60px;
}

.cashier .li1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.li1_img {
  width: 40px;
  height: 40px;
}

.cashier .li2 {
  color: white;
  font-size: 16px;
  line-height: 60px;
  text-align: center;
}

/*以上是导航条样式*/
.main {
  display: flex;
  position: relative;
  height: 100vh;
  overflow: hidden;
}

/* 主内容区域容器 */
.main-content {
  position: relative;
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.left {
  width: 100px;
  height: 100%;
}

.left_menu {
  position: relative;
  box-sizing: border-box;
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
  padding: 40px 0 40px 10px;
  width: 100px;
  height: 100vh;
  overflow: auto;
}

.left_menu ul {
  position: relative;
  z-index: 1;
}

.left_menu ul li {
  transition-duration: 150ms;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 6px 0px 0px 6px;
  height: 60px;
  color: #1c2024;
  font-size: 16px;
  line-height: 60px;
  text-align: center;
}

.left_menu ul li:hover {
  background: rgba(0, 0, 0, 0.05);
}

.left_menu ul li.addclass {
  background: none;
}

.f-left-nav-bg {
  position: absolute;
  left: 10px;
  z-index: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0px 7px 10px -5px rgb(62 99 221 / 30%);
  border-radius: 6px 0px 0px 6px;
  background: #fff;
  width: 90px;
  height: 60px;
}

/* 主体--右侧 */
.main-right {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
  width: 100%;
  height: 100%;
}

.server {
  border-right: 4px solid #ebeef7;
  width: 400px;
  height: calc(100vh);
}

.server_chioce {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  width: 400px;
  height: 60px;
  color: #3363ff;
}

.server_chioce_give {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 60px;
  color: #3363ff;
  /* border-bottom: 2px solid rgba(238, 238, 238, 1); */
}

.server_center {
  display: flex;
}

.b_left {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 4px 0px 0px 4px;
  padding: 8px 20px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.b_right {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 0px 4px 4px 0px;
  padding: 8px 20px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.b_mid_nocard {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 0px 4px 4px 0px;
  padding: 8px 20px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.b_mid_card {
  cursor: pointer;
  border: 1px solid #3363ff;
  padding: 8px 20px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.server_bg {
  background-color: #3363ff;
  color: #ffffff;
}

.server_line1 {
  background: rgba(238, 238, 238, 1);
  width: 100%;
  height: 2px;
}

.search_menu {
  /*height: calc(100vh - 202px);*/
  box-sizing: border-box;
  padding: 15px 15px 0 15px;
}

.search_menu_give {
  box-sizing: border-box;
  padding: 15px 15px 0 15px;
  width: 100%;
}

.search_bor {
  margin-bottom: 10px;
  width: 100%;
}

.search_label {
  display: flex;
  flex-wrap: wrap;
  margin: auto;
  width: 100%;
}

.search_label {
  /*display: flex;*/
}

.chooseLabelWrap {
  height: 40px;
}

.chooseLabel {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.chooseLabel::-webkit-scrollbar {
  height: 8px;
  overflow: hidden;
}

.chooseLabel::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  height: 8px;
}

.chooseLabel::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.chooseLabel > li {
  display: inline-block;
  margin-right: 10px;
}

.open_order_search {
  cursor: pointer;
  border: 1px solid #999999;
  border-radius: 4px;
  padding: 8px 15px;
  color: #999999;
  font-size: 14px;
}

.bg_label {
  border: 1px solid #3363ff;
  background: #3363ff;
  color: #fff;
}

.search_detail {
  width: 100%;
}

.serverWrap {
  box-sizing: border-box;
  height: calc(100vh - 130px);
  overflow-y: auto;
}

.serverWrap::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.serverWrap::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.serverWrap::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.serverWrap > li {
  display: inline-block;
  margin-right: 10px;
}

/* .serverWrap_give{
    height: calc(100vh - 360px);
    overflow-y: auto;
} */

.serverWrap_give {
  height: 390px;
  overflow-y: auto;
}
/* @media screen and (min-width: 1301px) {
    .serverWrap_give{
        height: calc(100vh - 360px);
        overflow-y: auto;
    }
} */

.serverWrap_give::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.serverWrap_give::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.serverWrap_give::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.serverWrap_give > li {
  display: inline-block;
  margin-right: 10px;
}

.search_detail1 {
  display: flex;
  transition-duration: 150ms;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 10px;
  width: 100%;
}

.search_detail1:hover {
  background: #f5f7fa;
}

.search_detail1:active {
  background-color: #3363ff;
  color: #fff;
}

.serach_detail_info {
  display: flex;
  align-items: center;
}

.serach_detail_img {
  /*border: 1px solid #f6f6f6;*/
  display: inline-block;
  box-sizing: border-box;
  margin-right: 8px;
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.service_name-price {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: calc(100% - 78px);
}
.loadingtip {
  margin: 10px 0 5px;
  height: 20px;
  color: rgb(77, 76, 76);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
}

.service_name {
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.service_name1 {
  margin-top: 5px;
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service_name_give {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.custom_dialog_give {
  width: 900px;
  height: 592px;
}

.custom_dialog_give .el-dialog__body {
  display: flex;
  flex-direction: row;
  padding: 0;
  height: 78vh;
  overflow: hidden;
}

.serach_detail_info_font {
  padding: 20px 0px 0px 20px;
}

.serach_detail_info_font1 {
  font-weight: bold;
  font-size: 18px !important;
}

.serach_detail_info_font2 {
  font-size: 18px !important;
  text-align: right;
}

.open_details {
  flex: 1;
  height: 100%;
}

.open_details_border {
  position: relative;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  height: 60px;
}
.open_details_bordercz {
  position: relative;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  height: 60px;
}

.open_details_title {
  font-weight: 400;
  font-size: 16px;
  line-height: 60px;
  text-align: center;
}

.open_details_title_font2 {
  position: absolute;
  top: 14px;
  right: 20px;
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 8px 20px;
  width: 56px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.open_details_info {
  box-sizing: border-box;
  /*padding: 15px 0;*/
  /* margin-bottom: 15px; */
}
.detailsHeight_give {
  /* 62 + 70 + 60 + 30+58 -60 */
  height: 405px;
  overflow: auto;
}

.detailsHeight {
  /* 62 + 70 + 60 + 30+58 -60 */
  height: calc(100vh - 262px);
  overflow: auto;
}

.detailsHeight2 {
  /* 62 + 70 + 60 + 30+58 -60*/
  height: calc(100vh - 333px);
  overflow: auto;
}

.open_details_info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.open_details_info::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.open_details_info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.search_open {
  box-sizing: border-box;
  padding: 15px;
  width: 100%;
}

.search_opne_input {
  flex: 1;
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.search_open img {
  width: 20px;
  height: 20px;
  /* cursor: pointer; */
}

.open_details_price {
  display: flex;
  align-items: center;
  padding-right: 15px;
}

.open_shop {
  flex: 1;
  cursor: pointer;
  box-sizing: border-box;
  border-left: 6px solid #3363ff;
  padding: 20px;
  height: 100%;
}

.open_server_name {
  box-sizing: border-box;
  padding: 0 15px;
  width: 100%;
  height: calc(100vh - 511px);
  overflow: auto;
}

.open_server_name_give {
  box-sizing: border-box;
  padding: 0 15px;
  width: 100%;
  max-height: 500px;
  overflow: auto;
}

.open_server_name::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.open_server_name::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.open_server_name::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.open_server_name .open_details_price {
  background: #f5f5f5;
}

.open_details_price_bg {
  background: #edf2fe;
}

.open_details_price_line {
  border: 1px solid #f5f5f5;
  background: #f5f5f5;
  width: 4px;
}

.open_details_price_line_bg {
  border: 1px solid #3363ff;
  background: #3363ff;
}

.font-weight {
  font-weight: 400;
}

.open_details_price_name,
.open_details_price_num,
.open_details_price_all {
  flex: 1;
}

.open_details_price_name {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.open_details_price_name_give {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
  line-height: 35px;
}

.open_details_price_num {
  color: black;
  font-weight: 400;
  font-size: 14px;
}

.open_details_price_num img {
  margin-top: 4px;
  width: 18px;
  height: 18px;
}

.open_details_price_all {
  min-width: 150px !important;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.open_details_price_all_give {
  margin-left: 24px;
  min-width: 130px !important;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
  line-height: 35px;
}

.el-input-number--mini .el-input-number__decrease,
.el-input-number--mini .el-input-number__increase {
  margin-top: 1px;
  width: 28px;
  font-size: 12px;
}

.open_details_price_all img {
  margin-top: 4px;
  width: 18px;
  height: 18px;
}

.open_details_price_del {
  cursor: pointer;
  line-height: 60px;
}

.change_all_price {
  box-sizing: border-box;
  border-left: 6px solid #3363ff;
  padding: 0 15px;
}

.useOffer,
.subtotal-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 15px 0;
  font-size: 14px;
}

.useOffer,
.subtotal_price_give {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 15px 0;
  /* justify-content: space-between; */
  font-size: 14px;
}

.useOffer {
  padding-bottom: 0;
}

.subtotal-price-inner {
  flex: 1;
  outline: 0;
  border: 0;
}

.offer-type {
  cursor: pointer;
  color: #3363ff;
}

.change_all_price_line {
  border: 1px solid #3363ff;
  background: #3363ff;
  width: 4px;
}

.change_all_price_font1 {
  color: #999999;
}

.change_all_price_font2 {
  display: flex;
  flex: 1;
  align-items: center;
  font-size: 14px;
}

.change_all_price_font2 img {
  margin-top: 1px;
  width: 15px;
  height: 15px;
  color: #333333 !important;
}

.change_all_price_font2 span {
}

.change_all_price_font3 {
  color: #999999;
  font-size: 14px;
}

.change_all_price_font3:hover {
  cursor: pointer;
}

.chioce_technician {
  display: flex;
  border-left: 6px solid #3363ff;
}

.chioce_technician_line {
  border: 1px solid #3363ff;
  background: #3363ff;
  width: 5px;
}

.chioce_technician_name,
.selective_sales_volume,
.batch {
  flex: 1;
  box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #f5f5f5;
  padding: 10px 15px;
}

.chioce_technician_name_font1 {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.chioce_technician_name_font1:hover {
  cursor: pointer;
}

.chioce_technician_name_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.selective_sales_volume_font1 {
  color: #3363ff;

  font-weight: 400;
  font-size: 14px;
}

.selective_sales_volume_font1:hover {
  cursor: pointer;
}

.selective_sales_volume_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.batch_font1 {
  cursor: pointer;
  color: #3363ff;

  font-weight: 400;
  font-size: 14px;
}

.batch_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.order_remark {
  box-sizing: border-box;
  padding: 0 15px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.remark_input {
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.zhk_remark_input {
  outline: none;
  border: 0;
  border: 1px solid;
  background-color: rgba(0, 0, 0, 0);
  padding: 6px 12px;
  width: 97%;
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.order_remark_font {
  height: 40px;
  font-size: 14px;
  line-height: 40px;
}

/*.order_remark_input {*/
/*height: 30px;*/
/*box-sizing: border-box;*/
/*padding: 0 15px;*/
/*border: 1px solid #E5E5E5;*/
/*}*/

/* .remark_input {
    font-size: 14px;
} */

.open_details_pay {
}

.chioce_Discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 15px;
  height: 58px;
}
.zhk_chioce_Discount {
  /* height: 58px; */
  /* display: flex; */
  /* justify-content: space-between; */
  /* align-items: center; */
  /* box-sizing: border-box; */
  /* padding-left: 30px; */
  border: 1px solid #e5e5e5;
}
.chioce_Discount_font1 {
  width: 50%;
  height: 34px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
  line-height: 34px;
}

.chioce_Discount_font0 {
  cursor: pointer;
  font-size: 16px;
}

.chioce_Discount_font2 {
  cursor: pointer;
  width: 60px;
  height: 20px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.chioce_Discount_font3 {
  width: 20px;
  height: 20px;
}

.open_details_pay_choice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 30px;
}

.open_details_pay_choice_font4,
.open_details_pay_choice_font2,
.open_details_pay_choice_font3 {
  cursor: pointer;
  box-sizing: border-box;
  padding: 20px 50px;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 18px;
}

.open_details_pay_choice_font1 {
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
}

.open_details_pay_choice_font2 {
  background: rgba(122, 122, 122, 1);
  width: 140px;
}

.open_details_pay_choice_font3 {
  background: rgba(43, 40, 44, 1);
  width: 140px;
}

.open_details_pay_choice_font4 {
  background: #3363ff;
  width: 140px;
}

.order_three {
  display: flex;
}

/*模态框 保存*/

.save_ok {
  position: absolute;
  top: 70%;
  left: 40%;
  opacity: 0.6;
  border-radius: 50px;
  background: rgba(0, 0, 0, 1);
  padding: 33px 100px;
  width: 105px;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  font-size: 20px;
}

/*模态框 选择技师*/
.xuanze_jishi {
  margin-bottom: 24px;
  padding-bottom: 24px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.xuanze_jishi_search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  /*border: 1px solid rgba(221, 221, 221, 1);*/
  border-radius: 4px;
}

.xuanze_jishi_name {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 10px 0px 10px 0px;
}

.xuanze_jishi_name_check {
}

.xuanze_jishi_name_font {
  cursor: pointer;
  margin-left: 18px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 17px;
}

.xuanze_jishi_server_font {
  padding-right: 15px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_jishi_server_switch {
  margin-left: 16px;
}

.over_save {
  display: flex;
  cursor: pointer;
}

.over_save_over {
  flex: 1;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 0px 0px 0px 7px;
  padding: 18px 0px 18px 0px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.over_save_save {
  flex: 1;
  border-radius: 0px 0px 7px 0px;
  background: #3363ff;
  padding: 18px 0px 18px 0px;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.xuazne_xiaoshou {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_piliang {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 48px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.xuanze_piliang_font {
  /*height: calc(100vh - 500px);*/
}

.piliang_jishi {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 23px 0px 23px 36px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.piliang_xiaoshou {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 23px 0px 23px 36px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.piliang_both {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 23px 0px 23px 36px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.qudan_font {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
}

.detail_say {
  border-radius: 4px;
  background: rgba(246, 246, 246, 1);
  padding: 10px 30px 10px 30px;
}

.detail_ul {
  display: flex;
  justify-content: space-between;
}

.detail_ul li {
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  font-size: 17px;
}

.qu_dan {
  padding-bottom: 48px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.shops {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}

.shops_num {
  /*flex:1;*/
  padding: 12px 0px 12px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.server_name {
  /*flex:1;*/
  padding: 21px 0px 21px 50px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

.shops_name {
  /*flex:1;*/
  padding: 21px 0px 21px 20px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.shops_name_font {
  padding: 5px 0px 0px 0px;
}

.shops_servername {
  /*flex:1;*/
  padding: 21px 30px 21px 0px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.shops_servername_font {
  padding: 5px 0px 0px 0px;
}

.shops_price {
  /*flex:1;*/
  padding: 21px 0px 21px 0px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

.shops_get_money {
  cursor: pointer;
  margin: 15px 5px 15px 0px;
  border-radius: 4px;
  background: #3363ff;
  padding: 10px 17px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

/*现金交易*/
.zj_xian_jin {
  width: 100%;
  /*margin: auto;*/
}

.receipt-wrap {
  width: 100%;
  height: calc(100vh - 60px);
}

.key-wrap {
  height: calc(100vh - 61px);
}

.zj_show_price {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid rgba(221, 221, 221, 1);
  background: rgba(255, 255, 255, 1);
  padding: 8px 25px;
  width: 100%;
}

.zj_show_price_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

.zj_font2_label {
  color: #3363ff;
}

.zj_show_price_font2 {
  display: flex;
  flex: 1;
  align-items: center;
  color: rgba(51, 51, 51, 1);
  font-size: 26px;
}

.zj_show_price_font2 > input {
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 26px;
}

/*计算输入值*/
.zj_price_menu {
  display: flex;
  align-items: center;
  width: 100%;
  /*120 46 55*/
  height: calc(100vh - 221px);
}

.zj_menu_num {
  width: 76%;
}

.zj_menu_take {
  width: 24%;
}

.zj_one_zero,
.zj_two_zero,
.zj_xiao_dian,
.zj_num_1,
.zj_num_2,
.zj_num_3 {
  transition-duration: 150ms;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin-right: 1%;
  margin-bottom: 8px;
  border: 1px solid rgba(221, 221, 221, 1);
  background: rgba(246, 246, 246, 1);
  width: 32%;
  height: 11vh;
  color: #333333;
  font-weight: bold;
  font-size: 36px;
  line-height: 11vh;
  text-align: center;
}

.zj_one_zero:hover,
.zj_two_zero:hover,
.zj_xiao_dian:hover,
.zj_num_1:hover,
.zj_num_2:hover,
.zj_num_3:hover,
.zj_menu_take_font1:hover,
.iconfont.iconshanchuyigeshuzi:hover {
  box-shadow: 8px 8px 20px -8px rgba(16, 66, 232, 0.2) inset;
}
.zj_menu_take_font2:hover {
  box-shadow: 8px 8px 20px -5px rgb(13, 58, 204) inset;
}

.zj_menu_take_font1,
.iconshanchuyigeshuzi,
.zj_menu_take_font2 {
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid rgba(221, 221, 221, 1);
  background: rgba(246, 246, 246, 1);
  width: 98.5%;
  height: 11vh;
  color: #333333;
  font-weight: bold;
  font-size: 36px;
  line-height: 11vh;
  text-align: center;
}

.iconshanchuyigeshuzi {
  font-size: 36px !important;
}

.zj_menu_take_font2 {
  background: #3363ff;
  height: 23.2vh;
  color: #fff;
  font-size: 30px;
  line-height: 23.2vh;
}

/*收银台充值所有样式*/
/*扫码充值*/
.saoma_chongzhi {
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  width: 400px;
  height: 60px;
}

.bt_saoma {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 5px;
  padding: 0px 8px 0px 8px;
  height: 40px;
  color: #3363ff;
  line-height: 40px;
}

.chongzhi_view {
  font-weight: 400;
  font-size: 16px;
}

.chonngzhi_vip_all {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0px;
}

.chongzhi_vip {
  display: flex;
  flex: 0.5;
}

.chongzhi_vip_info {
  width: 100px;
  height: 100px;
}

.chongzhi_vip_info1 {
  padding: 10px 0px 10px 40px;
}

.chongzhi_vip_info_font1 {
  padding-bottom: 15px;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 16px;
}

.chongzhi_vip_info_font2 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  width: 200px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhika_info {
  /*height: calc(100vh - 413px);*/
  overflow: auto;
}

.cz_height {
  /*60 + 62 + 30 + 112.19 + 60*/
  height: calc(100vh - 262px);
}

.cz_height2 {
  /*60 + 62 + 30 + 96 + 60*/
  height: calc(100vh - 248px);
}

.chongzhi_vip_info_font3 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhi_vip_info_font4 {
  flex: 0.5;
  cursor: pointer;
  padding-right: 3px;
  font-size: 20px !important;
  line-height: 90px;
  text-align: right !important;
}

.chaongzhika_name_money {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0;
}

.chongzhika_name_font {
  width: 150px;
  overflow: hidden;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chongzhika_money_font0 {
  flex: 1;
  text-align: right;
}

.chongzhika_money_font1 {
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
}

.chongzhika_money_font2 {
  box-sizing: border-box;
  padding-right: 50px;
}

.chongzhika_money_font3 {
  margin-bottom: 8px;
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.chongzhika_money_font4 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhika_money_font5 {
  cursor: pointer;
  width: 50px;
  text-align: center;
}

.chioce_paynum {
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.chongzhi_pay_num {
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0;
}

.chioce_paynum_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chioce_paynum_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_pay_cengson {
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0;
}

.vip_pay_cengson_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_pay_cengson_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou {
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0px 15px 0;
}

.vip_xuanze_xiaoshou1 {
  cursor: pointer;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou2 {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou3 {
}

.vip_add_zengson {
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 0px 15px 25px;
}

.add_zengson {
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

/*收款的样式*/

.chongzhi_shoukuan {
}

.chongzhi_shoukuan_font {
  flex: 1;
  cursor: pointer;
  background: #3363ff;
  padding: 20px 0px;
  width: 140px;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 18px;
  text-align: center;
}

/*支付二维码*/

.saoma_weweima {
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 10px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.zhifu_weweima {
  text-align: center;
}

.zhifu_weweima_img {
  width: 400px;
  height: 400px;
}

/*选择充值金额*/

.chongzhi_chongzhi_jine {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 24px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.chongzhi_jine_mian {
  display: flex;
  flex-wrap: wrap;
  max-height: 300px;
  overflow: auto;
}

.customize {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
}

.chongzhi_danka {
  box-sizing: border-box;
  margin: 0 10px;
  margin-bottom: 15px;
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 15px 25px;
  width: 150px;
}

.danka_get {
  margin-bottom: 10px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.danka_post {
  overflow: hidden;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.danka_post:hover {
  overflow: visible;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-overflow: inherit;
  white-space: pre-line; /*合并空白符序列，但是保留换行符。*/
}

/*充值页面添加服务样式*/

/*收银台套餐卡所有样式*/

/*套餐卡的css*/
.presonal_info {
  display: flex;
  padding: 6px;
}
.zhk_presonal_info {
  display: flex;
  padding: 14px 20px;
}

.bk_presonal_touxiang {
  display: flex;
  align-items: center;
}

.presonal_data {
  padding: 15px;
  width: 100%;
  height: 70px;
}

.presonal_tel {
  box-sizing: border-box;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 8px 0;
  width: 100%;
}

.tel_input {
  /*color: rgba(187, 187, 187, 1);*/
  margin-left: 15px;
  outline: none;
  border: 0;
  background: 0;
  font-weight: 400;
  font-size: 14px;
}

.presonal_name {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 12px 0;
  width: 100%;
}

.name_input {
  /*color: rgba(187, 187, 187, 1);*/
  margin-left: 15px;
  outline: none;
  border: 0;
  background: 0;
  font-weight: 400;
  font-size: 14px;
}

.gender {
  display: inline-block;
  cursor: pointer;
  border: 1px #3363ff solid;
  padding: 5px 10px;
  color: #3363ff;
}

.bk_sex_add {
  background: #3363ff;
  color: white;
}

/*卡的信息样式*/
.ka_server_one {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ka_server_one1 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}

.ka_one_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-align: left;
}

.ka_one_font2 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-align: right;
}

.ka_name_font1 {
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 14px;
}

.ka_name_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.ka_name_font3 {
  width: 100px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-align: right;
}

.iconlajitong {
  cursor: pointer;
  width: 100%;
  font-size: 20px !important;
  text-align: center;
}

.ka_detail {
  margin-top: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

/*从充次卡copy的增减样式*/
.ka_choice_num {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  line-height: 50px;
}

.open_details_num {
  flex: 1;
  margin-left: 20px;
  height: 50px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 50px;
}

.span1 {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(246, 246, 246, 1);
  padding: 4px 10px 4px 10px;
}

.span2 {
  border: 1px solid rgba(229, 229, 229, 1);
  border-right: none;
  border-left: none;
  padding: 4px 10px 4px 10px;
}

.span3 {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(246, 246, 246, 1);
  padding: 4px 8px 4px 8px;
}

.ka_zeng_jian {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.dist_power {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  line-height: 50px;
}

.dist_power_font2 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.dist_power_font3 {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.xiaoji_price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 12px 0;
}

.card-subtotal-money {
  display: flex;
  flex: 1;
  align-items: center;
}

.card-subtotal {
  cursor: pointer;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.change-money {
  flex: 1;
  box-sizing: border-box;
  margin: 0 10px;
  outline: none;
  border-radius: 4px;
  padding: 4px 0;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.tip {
  width: 100px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

/*选择销售*/
.chioce_xiaoshou1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  line-height: 50px;
}

.chioce_xiaoshou1_font2 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chioce_xiaoshou1_font3 {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

/*添加服务*/

.add_server1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  line-height: 50px;
}

.add_server1_font2 {
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

/*添加服务*/

.tianjia_fuwu {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.tianjia_fuwu_search {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  padding: 10px 5px 10px 5px;
}

.tianjia_fuwu_input {
  outline: none;
  border: 0;
  background: 0;
}

.tianjia_fuwu_mian {
  display: flex;
  margin-top: 10px;
  height: calc(100vh - 500px);
  overflow: auto;
}

.fuwu_biaoti {
  width: 20%;
}

.fuwu_biaoti_chioce {
  width: 79%;
}

.tianjia_fuwu_font {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  padding: 10px 10px 10px 10px;
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  font-size: 14px;
}

.tianjia_fuwu_font0 {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  background: #ebdcf2;
  padding: 10px 10px 10px 10px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.fuwu_biaoti_line {
  /*width: %;*/
  border: 0.5px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_chioce {
}

.fuwu_biaoti_chioce_bottom {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.server_biaoti_name_font {
  display: flex;
  justify-content: space-between;
  padding: 10px 0px 10px 0px;
  height: 30px;
  line-height: 30px;
}

.server_biaoti_name_font1 {
}

.server_biaoti_name_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.server_biaoti_name_font3 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
  text-align: right;
}

/*选择优惠权益*/

.select-offer > .el-dialog {
  margin: 0 auto;
}

.xuanze_qunayi {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 20px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.xuanze_qunayi_font0 {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_qunayi_font1 {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_qunayi_font2 {
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_qunayi_font3 {
  padding: 20px 20px 20px 30px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.use-offer-ul {
  max-height: 300px;
  overflow: auto;
}

.use-offer-ul::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.use-offer-ul::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.use-offer-ul::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.use-offer-li {
  display: flex;
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);

  padding: 20px 0px 20px 30px;
}

.offer-cardName {
  display: inline-block;
  width: 150px;
  white-space: normal;
}

.coupon-cardName {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
  width: 230px;
  white-space: normal;
}

.use-offer-li-coupon {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  /* display: flex; */
  padding: 20px 0px 10px 20px;
}

/*选择优惠样式*/

.xuanze_youhui_font1 {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 20px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.xuanze_youhui_font2 {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_youhui_font3 {
  margin-bottom: 200px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

/*收银台充次卡所有样式*/

.zhk_img1 {
  width: 40px;
  height: 40px;
}

.zhk_presonal_touxiang {
  display: flex;
  align-items: center;
}

.zhk_presonal_touxiang_img {
  width: 100px;
  height: 100px;
}

.zhk_serach_img {
  display: flex;
  align-items: center;
}

.zhk_serach_detail_font {
  padding-left: 20px;
}

.zhk_open_details_info {
  padding: 20px 3vh;
}

.zhk_presonal_data {
  padding-left: 20px;
  width: 100%;
}

.zhk_tel_input {
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.zhk_name_input {
  outline: none;
  border: 0;
  background: 0;
  width: 70%;
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.zhk_open_server_name {
  margin: auto;
  padding-top: 20px;
  height: calc(100vh - 428px);
  overflow: auto;
}

.zhk_open_shop {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  /* border-top: 1px solid rgba(229, 229, 229, 1); */
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 15px 15px;
  width: 100%;
}

.zhk_open_details_price_name {
  flex: 1;
  padding-top: 5px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.zhk_open_details_num {
  flex: 1;
  height: 50px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 50px;
}

.zhk_open_details_price_name1 {
  padding: 10px 0px;
}

.zhk_open_details_prices {
  flex: 1;
  line-height: 60px;
  text-align: center;
}

.zhk_chioce_Discount_font2 {
  margin-top: -25px;
  margin-left: 100px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.chioce_Discount_font1_check {
  flex: 1;
  /* text-align: center; */
}
.zhk_chioce_Discount_font1 {
  flex: 1;
}

.zhk_chioce_Discount1 {
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 30px;
  height: 58px;
}

.zhk_Discount_font1_input1,
.zhk_Discount_font1_input2 {
  outline: none;
  border: 1px solid rgba(153, 153, 153, 1);
  border-radius: 3px;
  background: 0;
  background: white;
  padding: 8px 16px;
  /* width: 60%; */
  color: #999999;
  font-size: 14px;
}

.zhk_chioce_Discount2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 30px;
  height: 58px;
}

.zhk_xingbeinv {
  cursor: pointer;
  border: 1px #3363ff solid;
  padding: 2px 2px 2px 2px;
  height: 25px;
  color: #3363ff;
  line-height: 25px;
  /*background: #B35DCC;*/
}

.zhk_xingbeinan {
  cursor: pointer;
  border: 1px #3363ff solid;
  padding: 2px 2px 2px 2px;
  height: 25px;
  /*background: #B35DCC;*/
  color: #3363ff;
  line-height: 25px;
}

.zhk_sex_add {
  background: #3363ff;
  color: white;
}

/*y一下是套餐卡css样式*/

.bk_server_center1 {
  cursor: pointer;
  border: 0.5px solid #3363ff;
  border-radius: 8px 0px 0px 8px;
  padding: 3px 10px 3px 10px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.bk_server_center2 {
  cursor: pointer;
  border: 0.5px solid #3363ff;
  padding: 3px 10px 3px 10px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.bk_server_center3 {
  cursor: pointer;
  border: 0.5px solid #3363ff;
  padding: 3px 10px 3px 10px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.bk_server_center4 {
  cursor: pointer;
  border: 0.5px solid #3363ff;
  border-radius: 0px 8px 8px 0px;
  padding: 3px 10px 3px 10px;
  color: #3363ff;
  line-height: 20px;
  text-align: center;
}

.bk_color_ka {
  background: #3363ff !important;
  color: white !important;
}

.bk_open_details {
  width: 100%;
}

.bk_presonal_touxiang_img {
  display: block;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
  width: 90px;
  height: 90px;
}

.bk_serach_img {
  display: flex;
  align-items: center;
}

.bk_serach_detail_font {
  padding-left: 20px;
}

.bk_open_details_info {
  box-sizing: border-box;
  padding: 11px 12px;
  height: calc(100vh - 177px);
  overflow: auto;
}

.bk_open_details_info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.bk_open_details_info::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.bk_open_details_info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.bk_presonal_data {
  padding-left: 20px;
  width: 100%;
}

.bk_tel_input {
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
  caret-color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.bk_name_input {
  flex: 1;
  outline: none;
  border: 0;
  background: 0;
  caret-color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.bk_open_server_name {
  /*height: calc(100vh - 428px);*/
}

/*以下是充值css样式*/

.chongzhi_vip_info_img {
  width: 100px;
  height: 100px;
}

.cz_open_details_info {
  padding: 16px 12px;
}

.zj_font1 {
  position: relative;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  height: 60px;
  color: rgba(0, 0, 0, 1);
  font-weight: 400;
  font-size: 18px;
  text-align: center;
}

.zj_font2 {
  box-sizing: border-box;
  padding: 15px 0;
  width: 100%;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 16px;
  text-align: center;
}

.zj_xuanze_vip {
  position: absolute;
  top: 15px;
  right: 20px;
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 8px 20px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.zj_font4 {
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  color: rgba(0, 0, 0, 1);
  font-weight: 400;
  font-size: 18px;
  line-height: 60px;
}

.cashier_open_order_Specifications_img {
  box-sizing: border-box;
  border: 1px solid #f6f6f6;
  padding: 5px;
  width: 70px;
  height: 70px;
}

.C_open_order_Specifications1 {
  display: flex;
  margin-bottom: 15px;
}

.C_open_order_Specifications_font {
  padding: 10px 0px 10px 20px;
}

.C_open_order_Specifications_font1 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.C_open_order_Specifications_font2 {
  padding-top: 10px;
  color: #999999;
  font-weight: 400;
  font-size: 12px;
}

.C_open_order_Specifications2 {
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 0;
}

.C_open_order_Specifications2_font1 {
  padding-bottom: 5px;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.C_open_order_Specifications2_font2 {
  display: flex;
  padding: 10px 0px 5px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 12px;
}

.C_open_order_Specifications2_font3 {
  cursor: pointer;
  margin-right: 10px;
  border: #999999 1px solid;
  border-radius: 4px;
  padding: 8px 20px;
}

.C_open_order_Specification2 {
  margin-bottom: 30px;
  max-height: 300px;
  overflow: auto;
}

.C_Specifications_choice1_add {
  border: 1px solid #3363ff !important;
  background: #3363ff !important;
  color: white !important;
}

.open_details_price_name_font1 {
  color: black;
  font-weight: 400;
  font-size: 14px;
}

.open_details_price_name_font2 {
  padding-top: 5px;
  color: #999999;
  font-weight: 400;
  font-size: 12px;
}
.open_details_price_name_font3 {
  cursor: pointer;
  border-left: 6px solid #3363ff;
  padding: 0px 0px 10px 20px;
  color: #ee6911;
  font-weight: 400;
  /*padding-top: 5px;*/
  font-size: 14px;
}

.el-input__icon {
  cursor: pointer;
}

.servers_none_show_y {
  display: block;
}

.servers_none_show_n {
  display: none;
}

.cancel-btn:focus,
.cancel-btn:hover {
  border-color: #dcdfe6;
  background-color: #fff;
  color: #3363ff;
}

/* 5- 30 */

.queryMemberInfo {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid #dcdfe6;
  padding: 15px 0;
}

/*充次卡会员样式*/
.zhkqueryMemberInfo {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid #dcdfe6;
  padding: 15px 0;
}

/*充次卡个人信息样式*/
.zuk_presonal_info {
  display: flex;
  padding: 20px;
}

.member-pic {
  margin-right: 15px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
  width: 80px;
  height: 80px;
}

.member-pic > img {
  display: inline-block;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  border-radius: 50px;
  width: 100%;
  height: 100%;
}

.member-info-wrap {
  display: flex;
  flex: 1;
  justify-content: space-between;
  font-size: 14px;
}

.member-name {
  width: 250px;
  overflow: hidden;
  font-weight: 600;
  font-size: 18px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-info {
  flex: 1;
  line-height: 20px;
}

.member-del {
  width: 50px;
  text-align: center;
}

/*取单*/

/*下单收款*/
.cz_qudan_top {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
  width: 100%;
  height: 60px;
  /* background: #2b282c; */
  /* color: #fff; */
  font-size: 18px;
}

.title-left,
.left_main {
  width: 400px;
}

.left_main {
  border-right: 1px solid rgb(243 244 246);
}

.cash-register {
  display: flex;
}

.left_mid {
  box-sizing: border-box;
  border-right: 3px solid #f6f6f6;
  padding: 15px 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.left_mid::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.left_mid::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.left_mid::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.order_info_wrap {
  display: flex;
}

.order_info_index {
  box-sizing: border-box;
  margin-right: 8px;
}

.order_item {
  flex: 1;
  margin-bottom: 8px;
  border-bottom: 1px solid #e5e5e5;
}

.order_info_li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.code-attention {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.border-margin {
  margin-bottom: 10px;
  border-bottom: 2px solid #f6f6f6;
}

.shoucang_img {
  margin-bottom: 10px;
  width: 100px;
  height: 100px;
}

.shoucang_img > img {
  width: 100%;
  height: 100%;
}

.left_but {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  background: #f6f6f6;
  padding: 0 15px;
  height: 60px;
  color: #000;
  font-size: 18px;
}

.right_main {
  width: calc(100vw - 400px);
  /* height: calc(100vh - 60px); */
}

.cash-register-type {
  box-shadow: 0 0 10px 0 inset rgba(0, 0, 0, 0.1);
}

.f-line-b {
  flex: 1;
  box-sizing: border-box;
  border-bottom: 1px solid rgb(243 244 246);
}

.f-line-b-l {
  box-shadow:
    25px 0px 20px 0px #fff inset,
    0px -15px 15px -18px rgba(0, 0, 0, 0.15) inset;
}

.f-line-b-r {
  box-shadow:
    -25px 0px 20px 0px #fff inset,
    0px -15px 15px -18px rgba(0, 0, 0, 0.15) inset;
}

.pay-type {
  cursor: pointer;
  margin: 0 10px;
  background: #f6f6f6;
  width: 150px;
  height: 50px;
  font-size: 16px;
  line-height: 50px;
  text-align: center;
}

.debt {
  position: absolute;
  top: 88px;
  right: 20px;
}

.pay-typeActive {
  box-shadow: -5 0 10px -3 inset rgba(0, 0, 0, 0.1);
  /* color: #3363FF; */
  border-radius: 10px 10px 0 0;
  background: #fff;
}

.pay-key-wrap {
  height: calc(100vh - 166px);
}

.zj_font3 {
  margin: 0 auto;
  width: 80%;
}

.receiptMemberInfo {
  box-sizing: border-box;
  margin-top: 15px;
  box-shadow: 0px 0px 1px 2px #f6f6f6;
  padding: 15px;
}

.member_info {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 14px;
}

.member_info > .el-icon-remove {
  cursor: pointer;
  margin-right: 10px;
  color: #3363ff;
  font-weight: 400;
  font-size: 20px;
}

.receiptMemberInfo > li {
  margin-bottom: 8px;
}

.receiptMemberInfo > :last-child {
  margin-bottom: 0;
}

.cz_kaidan .el-dialog__header {
  padding: 0;
}

.cz_kaidan .el-dialog__body {
  padding: 0;
}

.kd_weixin {
  margin: auto;
  width: 100%;
  text-align: center;
}

.kd_weixin > div:nth-child(1) {
  margin-top: 30px;
  color: #333333;
  font-weight: 400;
  font-size: 20px;
}

.kd_weixin > div:nth-child(2) {
  margin: 20px 0 30px 0;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.kd_weixin > div:nth-child(4) > input {
  margin: 30px 0 50px 0;
  border: 1px solid #3363ff;
  width: 350px;
  height: 44px;
  text-align: center;
}

.kd_bottom {
  width: 100%;
  color: #3363ff;
  font-size: 14px;
  text-align: center;
}

.right_font3_title {
  margin: 30px 0 30px 0;
  width: 100%;
  color: #333333;
  font-weight: 400;
  font-size: 20px;
  text-align: center;
}

.kd_server_list_czk {
  cursor: pointer;
  margin-bottom: 10px;
  margin-left: 20px;
  border-radius: 5px;
  background-color: #f6f6f6;
  width: 45%;
  height: 90px;
}

.kd_server_list_czk > div:nth-child(1) {
  padding: 15px 15px;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 14px;
}

.kd_server_list_czk > div:nth-child(2) {
  padding-bottom: 5px;
  padding-left: 13px;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 22px;
}

.kd_server_list_czk > div:nth-child(3) {
  padding-bottom: 15px;
  padding-left: 18px;
  color: #bbbbbb;
  font-size: 12px;
}

.kd_yue {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 300px;
  overflow: auto;
}

.kd_yue::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.kd_yue::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.kd_yue::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.kd_czk_list_active {
  border: 1px solid #cccccc;
}

.right_font3_button {
  margin: auto;
  width: 100%;
  text-align: center;
  /*margin-top: 30px;*/
}

.right_font3_button > .el-button--medium {
  border-radius: 4px;
  padding: 15px 35px;
  font-size: 16px;
}

/* 扣卡 */
.search_menu-card {
  /*box-sizing: border-box;*/
  /*padding-top: 15px;*/
}

.consumption-ul {
  display: flex;
  box-sizing: border-box;
  padding: 15px;
}

.consumption-li {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.consumption-info {
  margin-bottom: 10px;
}

.secondary-card-equity {
  box-sizing: border-box;
  background: #eeeeee;
  padding: 12px 15px;
}

.search_menu-card > .search_detail {
  box-sizing: border-box;
  /* padding: 0 15px; */
  height: calc(100vh - 68px);
  overflow: auto;
}

.search_menu-card > .search_detail::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.search_menu-card > .search_detail::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.search_menu-card > .search_detail::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.newMemberCardGoods {
  display: flex;
  position: relative;
  flex-direction: row;
  border-top: 1px solid #ccc;
  padding: 8px;
}

.newMemberCardInfo .el-collapse-item__content {
  padding-bottom: 0;
}

.newMemberCardItem .newMemberCardGoods:last-child {
  display: flex;
  position: relative;
  flex-direction: row;
  border-bottom: 1px solid #ccc;
  padding: 8px;
}

.newMemberCardGoods .newMemberCardGoodsSercice {
  text-align: left;
}

.newMemberCardGoods .newMemberCardGoodsStatusName {
  position: absolute;
  right: 15px;
  text-align: right;
}

.newMemberCardGoodsWhiteStatusName {
  margin-top: 5px;
  color: #fff;
}

.newMemberCardGoodsSercice > p:first-child {
  margin-top: 10px;
}

.newMemberCardGoodsSercice > p:last-child {
  margin-top: 14px;
}

.newMemberCardGoodsStatusName > p:first-child {
  margin-top: 10px;
}

.newMemberCardGoodsStatusName > p:last-child {
  margin-top: 14px;
}

.card-price-num {
  display: flex;
  justify-content: space-between;
}

.takeOrderName > td {
  font-size: 14px;
}

.el-popup-parent--hidden {
  padding-right: 0 !important;
}

.cancelButtonClass:focus,
.cancelButtonClass:hover {
  border-color: #dcdfe6;
  background-color: #fff;
}

/*充次卡的新加样式*/
.zuheka-wrap {
  width: 100%;
  height: 100%;
}
.zhkVipHead {
  display: inline-block;
  vertical-align: middle;
  width: 80px;
  height: 80px;
}
.presonal_touxiang {
  height: 100px;
  line-height: 100px;
}
.vip_sex_show {
  cursor: pointer;
  border: 1px solid #3363ff;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  background: #3363ff;
  padding: 5px 10px;
  color: white;
  font-weight: 400;
  font-size: 14px !important;
}
.vip_sex_none {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  padding: 5px 10px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px !important;
}
.open_price_name_font1 {
}
.open_price_name_font1 {
  padding-bottom: 10px;
}
.zhk_order_remark_font {
  height: 35px;
  font-size: 14px;
  line-height: 40px;
}
.zhk_order_remark_input {
  box-sizing: border-box;
  margin-bottom: 10px;
  /* padding: 0 15px; */
  width: 100%;
  height: 30px;
  /* border: 1px solid; */
  line-height: 24px;
}

/* 订单列表样式 */
.demo-table-expand .el-form-item {
  margin-bottom: 0;
}

/* 修改员工业绩部分样式 */
.performance_list {
  margin-bottom: 10px;
  /* box-shadow: 0 2px 12px 0 rgba(0,0,0,.1); */
  padding: 8px;
}
.performance_list_item {
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 8px;
}
.performance_list > li .performance_card {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: auto;
  margin-bottom: 10px;
  border-radius: 5px;
  background-color: #f1e8e8;
  width: 80%;
  height: 40px;
  line-height: 40px;
}
.performance_title {
  display: flex;
  justify-content: space-around;
  height: 40px;
  font-weight: bold;
  font-size: 16px;
  line-height: 40px;
}
.performance_row {
  display: flex;
  align-items: center;
  padding: 5px 0;
}
.performance_row > div:first-child {
  width: 14%;
  min-height: 30px;
  text-align: center;
}
.performance_row ul {
  width: 100%;
}
.performance_row ul li {
  display: flex;
  justify-content: space-around;
  margin-bottom: 8px;
}
.performance_row .performance_group_input {
  display: flex;
}
.performance_row .performance_group_input .el-input-group {
  width: initial;
}

.performance_row ul li input {
  width: 80px;
}
.performance_row ul li input[type="number"] {
  -moz-appearance: textfield;
}
.performance_row ul li input[type="number"]::-webkit-inner-spin-button,
.performance_row ul li input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.performance_row ul li .choose_deduct input {
  padding: 0;
  width: 83px;
}
/* 选择销售手艺人弹框 */
.kd_add_box > div {
  width: 40%;
  height: 500px;
  overflow: auto;
}
.kd_add_box > div::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.kd_add_box > div::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}
.kd_add_box > div::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}
.sales_content {
  display: flex;
  flex-wrap: wrap;
}
.sales_content div {
  margin: 0 10px 10px 0;
}

@media screen and (max-width: 1200px) {
  .zj_show_price1 {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin-right: 20px;
    border: 1px solid rgba(221, 221, 221, 1);
    background: rgba(255, 255, 255, 1);
    padding: 8px 25px;
    width: 81%;
  }

  .return-money {
    margin-top: 200px;
    color: #999;
    font-weight: 600;
    font-size: 20px;
    text-align: center;
  }
  .return-money span {
    display: block;
  }

  .return-money-btn {
    margin-top: 100px;
    font-size: 20px;
  }
}

@media screen and (min-width: 1201px) {
  .return-money {
    margin-top: 300px;
    color: #999;
    font-weight: 600;
    font-size: 30px;
    text-align: center;
  }
  .return-money span {
    display: block;
  }

  .return-money-btn {
    margin-top: 150px;
    font-size: 26px;
  }
  .zj_show_price1 {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin-right: 20px;
    border: 1px solid rgba(221, 221, 221, 1);
    background: rgba(255, 255, 255, 1);
    padding: 8px 25px;
    width: 90%;
  }
}

.zj_show_price1_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

.zj_font2_label {
  color: #3363ff;
}

.zj_show_price1_font2 {
  display: flex;
  flex: 1;
  align-items: center;
  color: rgba(51, 51, 51, 1);
  font-size: 26px;
}

.zj_show_price1_font2 > input {
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 26px;
}

.pay_type_tag {
  cursor: pointer;
  margin: 0px 20px 20px 0px;
  border: 1px solid #bbbbbb;
  background: #fff;
  padding: 10px 22px;
  height: 52px;
  color: #606266;
  font-size: 16px;
  line-height: 30px;
}

.pay_type_active {
  border: 1px solid #3363ff;
  background: #3363ff;
  color: #fff;
}

.pay_type_div {
  text-align: -webkit-right;
}

.pay_type_money {
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid rgba(221, 221, 221, 1);
  background: #3363ff;
  width: 24%;
  height: 10vh;
  color: #fff;
  font-size: 24px;
  line-height: 10vh;
  text-align: center;
}

/* 收银台核销 */
.verify_search {
  display: flex;
  flex-direction: row;
  margin: 80px auto 0 auto;
  width: 60%;
}

.verify_input {
  border: 1px solid #ddd;
  border-radius: 30px 0 0 30px;
  width: 84%;
  height: 58px;
}

.verify_input input {
  margin-left: 36px;
  outline: none;
  border: 0px;
  width: 100%;
  font-size: 20px;
  line-height: 58px;
}

.verify_btn {
  cursor: pointer;
  /* border: 1px solid #ddd; */
  border-radius: 0 10px 10px 0;
  background: #3363ff;
  width: 16%;
  height: 60px;
}

.verify_btn button {
  /* display: inherit; */
  outline: none;
  border: 0px;
  width: 100%;
  height: 60px;
  color: #fff;
  /* margin-left: 28px; */
  font-size: 20px;
}

.verify_display {
  margin-top: 80px;
  width: 100%;
  text-align: center;
}

.verify_span {
  cursor: default;
  margin-top: 10px;
  font-size: 20px;
}

.verify_card {
  margin: 36px auto 0 auto;
  box-shadow: 0 0 10px #d1d1d1;
  padding-top: 30px;
  width: 60%;
}

.verify_server {
  display: flex;
  flex-direction: row;
  margin: auto;
  box-shadow: 0 0 10px #d1d1d1;
  padding: 20px 30px;
  width: 82%;
  height: 90px;
  overflow: hidden;
  overflow-y: auto;
}

.verify_server_name {
  padding-left: 50px;
}

.verify_ul {
  margin: auto;
  width: 88%;
}

.verify_appointment {
  border-bottom: 1px solid #d1d1d1;
  width: 100%;
  height: 44px;
  line-height: 44px;
}

.verify_ul_remark {
  margin-top: 16px;
}

.verify_ul_btn {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin: 20px auto 0 auto;
  padding-bottom: 20px;
  width: 100%;
  text-align: center;
}
.verify_ul_btn button {
  border: 1px solid #3363ff;
  border-radius: 2px;
  width: 120px;
  height: 30px;
  /* background: #B35DCC; */
  color: #3363ff;
}
.fetchOrderDialog .el-dialog__body {
  margin-top: 10px;
  padding: 0 20px 30px 20px;
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.fetchOrderDialog
  .el-table--scrollable-y
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.fetchOrderClass {
  display: flex;
  flex-direction: row;
}

.fetchOrderClass .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 330px;
}

/* 查看耗材 */
.costMaterial {
  height: 270px;
  overflow: hidden;
  overflow-y: auto;
  text-align: left;
}

.costMaterial::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.costMaterial::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.costMaterial::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/* .switchStyle .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  .switchStyle .el-switch__label--left {
    z-index: 9;
    left: 6px;
  }
  .switchStyle .el-switch__label--right {
    z-index: 9;
    left: -7px;
  }
  .switchStyle .el-switch__label.is-active {
    display: block;
  }
  .switchStyle.el-switch .el-switch__core,
  .el-switch .el-switch__label {
    width: 50px !important;
  } */

/* 主内容区域动画效果 */
.main-right {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
  width: 100%;
  height: 100%;
}

/* 主页面切换动效 */

/* 向上过渡效果（从大到小导航） */
.slide-up-enter {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-enter-active {
  transition:
    transform 0.1s ease-out,
    opacity 0.1s ease-out;
}

.slide-up-leave-active {
  transition:
    transform 0.1s ease-in,
    opacity 0.1s ease-in;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

/* 向下过渡效果（从小到大导航） */
.slide-down-enter {
  transform: translateY(-30px);
  opacity: 0;
}

.slide-down-enter-active {
  transition:
    transform 0.1s ease-out,
    opacity 0.1s ease-out;
}

.slide-down-leave-active {
  transition:
    transform 0.1s ease-in,
    opacity 0.1s ease-in;
}

.slide-down-leave-to {
  transform: translateY(30px);
  opacity: 0;
}

.f-payment-btn {
  display: flex;
  position: absolute;
  justify-content: center;
  align-items: center;
  z-index: 200;
  transition: all 0.2s ease;
  will-change: box-shadow, transform;
  cursor: pointer;
  outline: 0;
  border: 0;
  border-radius: 0.3em;
  background: #3363ff;
  height: 2.6em;
  color: #fff;
  font-size: 22px;
  text-shadow: 0 1px 0 rgb(0 0 0 / 40%);
}

.f-payment-btn:hover {
  transform: translateY(-0.1em);
  box-shadow:
    0px 0.1em 0.2em rgba(62, 99, 221, 0.66),
    0px 0.4em 0.7em -0.1em rgba(62, 99, 221, 0.54),
    inset 0px -0.1em 0px #3363ff;
}

.f-payment-btn:active {
  transform: translateY(0em);
  box-shadow: inset 0px 0.1em 0.6em #3363ff;
}

.f-payment-money-box {
  box-shadow: 7px 7px 10px -5px #3363ff80 inset;
}
