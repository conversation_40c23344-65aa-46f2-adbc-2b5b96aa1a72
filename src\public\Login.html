<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <title>登录</title>
    <link rel="stylesheet" href="css/css-comment.css" />
    <link rel="stylesheet" href="vue/element/<EMAIL>" />
    <link rel="stylesheet" href="css/shouyingtai_kaidan.css" />
    <link rel="stylesheet" href="css/0-login.css" />
    <link
      rel="stylesheet"
      href="http://at.alicdn.com/t/font_1156348_if4g6jkesri.css"
    />
    <style>
      .el-icon-paperclip:before {
        content: "\e77d";
      }

      .iconyuming {
        cursor: pointer;
        color: #3363ff;
      }

      .progressMask {
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.8);
        width: 100%;
        height: 100%;
      }

      .progress {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .el-progress__text {
        color: #fff;
      }

      .seeDomainName {
        cursor: pointer;
        color: #ff5a42;
        font-size: 14px;
      }
      .f-main-body-border::after {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        border: 2px solid #bbbbbb;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        content: "";
      }
    </style>
  </head>

  <body class="f-main-body-border" style="background-color: rgba(0, 0, 0, 0)">
    <div class="login-Wrap drag">
      <div id="app" v-cloak>
        <div class="bg_login" @keyup.enter.exact="keyLogin">
          <div class="input_login no-drag">
            <div>
              <!---->
              <div class="flex w-full justify-center">
                <div class="flex space-x-2">
                  <img
                    src="./images/logo.png"
                    alt="logo"
                    style="width: 76px; height: 21px"
                  />
                  <div class="bg-black/90 text-white rounded px-2 py-1 text-xs">
                    收银系统
                  </div>
                </div>
              </div>
              <div class="form-wrap">
                <el-form>
                  <el-form-item>
                    <el-alert
                      v-show="errTip"
                      style="line-height: 0"
                      :title="errTip"
                      type="error"
                      :closable="false"
                      effect="dark"
                    ></el-alert>
                  </el-form-item>
                  <el-form-item>
                    <div class="el-input el-input--prefix">
                      <input
                        ref="phone"
                        type="text"
                        autocomplete="off"
                        maxlength="11"
                        placeholder="请输入内容"
                        prefix=""
                        v-model.trim="telephone"
                        @input="telInput(telephone)"
                        class="el-input__inner"
                      />
                      <span class="el-input__prefix">
                        <span class="login-label">手机号</span>
                      </span>
                    </div>
                  </el-form-item>
                  <el-form-item style="margin-bottom: 10px">
                    <div class="el-input el-input--prefix el-input--suffix">
                      <input
                        ref="password"
                        v-model.trim="password"
                        type="password"
                        autocomplete="off"
                        placeholder="请输入内容"
                        suffix=""
                        @input="telInput(telephone)"
                        prefix=""
                        class="el-input__inner"
                      />
                      <span class="el-input__prefix">
                        <span class="login-label">密码</span>
                      </span>
                      <span class="el-input__suffix">
                        <span class="el-input__suffix-inner">
                          <span class="login-label" style="text-align: center">
                            <i
                              @click="bindeEye(1)"
                              v-if="eye"
                              class="iconfont iconpassword2"
                            ></i>
                            <i
                              @click="bindeEye(0)"
                              v-else
                              class="iconfont iconpassword"
                            ></i>
                          </span>
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
              <div style="text-align: right; margin-bottom: 10px">
                <el-checkbox v-model="remember">记住账号</el-checkbox>
                <i
                  class="iconfont iconyuming"
                  title="查看域名"
                  @click="seeDomainName"
                ></i>
              </div>
              <el-button
                class="login"
                type="primary"
                @click="login"
                :disabled="loginLoad"
                :loading="loginLoad"
              >
                登&emsp;录
              </el-button>
            </div>
          </div>
        </div>
        <i class="el-icon-close close no-drag" @click="closeLogin"></i>

        <p class="version w-full text-center">版本号:{{version}}</p>
        <el-dialog
          title="发现新版本"
          class="newVersion"
          :visible.sync="centerDialogVisible"
          :close-on-click-modal="false"
          width="40%"
          center
        >
          <p class="version-title">当前版本:{{version}}</p>
          <p class="version-title">更新版本:{{newVersion}}</p>
          <p class="version-title">更新内容:</p>
          <ul class="version-ul">
            <pre
              style="white-space: pre-wrap"
            ><li style="white-space: normal;" v-html="desc"></li></pre>
          </ul>
          <div slot="footer" class="dialog-footer">
            <el-button class="later" @click="centerDialogVisible = false">
              稍后更新
            </el-button>
            <el-button type="primary" @click="updateImmediately">
              立即更新
            </el-button>
          </div>
        </el-dialog>
        <div class="progressMask" v-if="progress"></div>
        <el-progress
          class="progress"
          v-if="progress"
          type="circle"
          :percentage="width"
          color="#E93E4E"
        ></el-progress>
      </div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <!-- <script src="https://unpkg.com/hotkeys-js/dist/hotkeys.min.js"></script> -->
  <script>
    try {
      var gui = require("nw.gui");
      var win = gui.Window.get();
      var request = require("request");
      var fs = require("fs");
      win.show();
      /*       var nwV = gui.process.versions.nw;

      if (nwV == "0.35.5") {

        // nw.Window.get().evalNWBin(null, 'binary.bin');
      } else {
        nw.Window.get().evalNWBin(null, "app.bin");
      } */
    } catch (e) {}

    const defaultSize = {
      w: 1200,
      h: 750,
    };
    function openCashier() {
      nw.Window.open(
        "./public/header.html",
        {
          // fullscreen: true,
          position: "center",
          frame: false,
          width: 1300,
          height: 768,
          min_width: 1200,
          min_height: 750,
        },
        function (win) {
          window.close();
          win.on("closed", function () {
            win = null;
          });
        }
      );
    }

    var app = new Vue({
      el: "#app",
      data() {
        const telephone = localStorage.getItem("loginInfo_telephone");
        // const telephone = "13809217687";
        return {
          telephone: telephone ? telephone : "",
          // password: "217687",
          password: "",
          errTip: "",
          eye: true,
          domainName: "",
          url: "",
          loginLoad: false,
          loginInfo: {},
          logo: {},
          // 版本
          updater: "",
          gui: "",
          width: 0,
          version: "",
          newVersion: "",
          progress: false,
          timer: null,
          weighing: "",
          desc: "",
          centerDialogVisible: false,
          paySuccessOpen: true, //后台是否开启员工业绩修改
          // 记住账号
          remember: telephone ? true : false,
          telephoneArr: [],
        };
      },
      mounted: function () {
        this.getLoginInfo();
        this.getFouce(this.$refs.phone);
        this.getdomainName();
        this.checkUpdate();
        this.getLogo();
      },
      methods: {
        getdomainName() {
          var self = this;
          var d = localStorage.getItem("fdb-domainName");
          if (d) {
            self.domainName = d;
          } else {
            // self.domainName = "http://192.168.8.188";
            self.domainName = "https://store.fsyxgkj.com";
            localStorage.setItem("fdb-domainName", self.domainName);
          }
          self.url = self.domainName + "/index.php?s=";
          /*           $.ajax({
            url: self.url + "/domainName",
            type: "get",
            success: function (res) {

              if (res.status == 1) {
                self.domainName = res.data;
              } else {
                self.$message({
                  message: "您还未设置域名,请设置域名",
                  duration: 1500,
                  type: "error",
                  onClose: function () {
                    self
                      .$prompt("", "设置域名", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        inputPattern:
                          /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,
                        inputErrorMessage: "域名输入有误",
                        closeOnClickModal: false,
                      })
                      .then(({ value }) => {
                        self.domainName = value;
                        self.setDomainName(self.domainName);
                      })
                      .catch(() => {
                        self.$message({
                          type: "info",
                          message: "未设置域名,您将不能登录",
                        });
                      });
                  },
                });
              }
            },
          }); */
        },

        // 查看域名
        seeDomainName: function () {
          var self = this;
          self
            .$prompt("", "设置域名", {
              inputValue: self.domainName,
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              placeholder: "例如：http://www.baidu.com",
              // inputPattern:
              //   /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,
              // inputErrorMessage: "域名输入有误",
              closeOnClickModal: false,
            })
            .then(({ value }) => {
              self.domainName = value;
              // self.setDomainName(self.domainName);
              localStorage.setItem("fdb-domainName", self.domainName);
              self.getdomainName();
            })
            .catch(function () {
              //取消
            });
        },

        /* setDomainName(url) {
          var setUrl = url;
          var self = this;
          $.ajax({
            url: self.url + "/android/setDomainName",
            type: "post",
            data: {
              url: setUrl,
            },
            success: function (res) {
              if (res.status == 1) {
                self.$message({
                  type: "success",
                  message: res.data,
                });
                self.getLogo(1);
              }
            },
          });
        }, */

        closeLogin: function () {
          win.close();
        },

        getLoginInfo: function () {
          if (localStorage.getItem("loginInfo")) {
            this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
          }
          // TODO 方便调试
        },

        getLogo: function (flag) {
          var _self = this;
          if (!flag) {
            let LoginLogoUrl = localStorage.getItem("LoginLogoUrl");
            // console.log(LoginLogoUrl);
            if (LoginLogoUrl) {
              return (_self.logo = JSON.parse(LoginLogoUrl));
            }
          }
          $.ajax({
            url: _self.url + "/android/Login/logo",
            type: "post",
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.logo = res.data;
                localStorage.setItem("LoginLogoUrl", JSON.stringify(res.data));
              }
            },
          });
        },

        bindeEye: function (type) {
          switch (type) {
            case 0:
              this.eye = true;
              this.$refs.password.type = "password";
              break;
            case 1:
              this.eye = false;
              this.$refs.password.type = "text";
              break;
          }
        },

        getFouce: function (dow) {
          this.$nextTick(
            function () {
              dow.focus();
            }.bind(this)
          );
        },

        telInput: function (val) {
          if (val != "" || val == 11) {
            this.errTip = "";
          }
        },

        login: function () {
          // TODO 方便调试
          // openCashier();
          var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
          if (!this.telephone) {
            this.errTip = "手机号或密码不能为空";
            this.getFouce(this.$refs.phone);
          } else if (!reg.test(this.telephone)) {
            this.errTip = "手机号码输入有误";
            this.getFouce(this.$refs.phone);
          } else if (!this.password) {
            this.errTip = "请输入正确密码";
            this.getFouce(this.$refs.password);
          } else {
            this.loginLoad = true;
            this.subLogin();
          }
        },

        keyLogin: function () {
          this.login();
        },

        // 登录
        subLogin: function () {
          var self = this;
          let ajaxTimeOut = $.ajax({
            url: self.url + "/android/Login/login",
            type: "post",
            data: {
              phone: self.telephone,
              pass: self.password,
            },
            timeout: 10000,
            success: function (res) {
              //   // var res = JSON.parse(res);
              if (res.code == 1) {
                localStorage.setItem("loginInfo", JSON.stringify(res.data));
                if (self.remember) {
                  localStorage.setItem("loginInfo_telephone", self.telephone);
                } else {
                  localStorage.removeItem("loginInfo_telephone");
                }
                if (res.data.technicianName) {
                  localStorage.setItem(
                    "globalTechnicianName",
                    res.data.technicianName
                  );
                } else {
                  localStorage.removeItem("globalTechnicianName");
                }
                // location.href = "cashier_system.html";
                openCashier();
                self.telephone = self.password = self.errTip = "";
                self.loginLoad = false;

                if (
                  res.data.pay_success_open == 1 &&
                  res.data.is_edit_authority == 1
                ) {
                  global.login = 1;
                } else {
                  global.login = 0;
                }
              } else {
                self.errTip = res.msg;
                self.password = "";
                self.loginLoad = false;
              }
            },
            complete: function (XMLHttpRequest, status) {
              //当请求完成时调用函数
              if (status == "timeout") {
                //status == 'timeout'意为超时,status的可能取值：success,notmodified,nocontent,error,timeout,abort,parsererror
                ajaxTimeOut.abort(); //取消请求
                self.loginLoad = false;
                self.$message({
                  type: "info",
                  message: "请求超时,请重试",
                  duration: 1500,
                });
              }
            },
          });
        },

        //立即跟新
        updateImmediately() {
          this.centerDialogVisible = false;
          // this.loading2 = true;
          // this.loadingText = "软件包下载...";
          this.progress = true;
          this.update();
        },

        downloadNewVersion(url, toUrl, cb) {
          var downloadApi = url;
          var filePath = toUrl;
          var stream = fs.createWriteStream(filePath);
          var self = this;

          request.head(downloadApi, function (err, res, body) {
            self.timer = setInterval(function () {
              var width =
                (fs.statSync(filePath).size / res.headers["content-length"]) *
                100;
              self.width = parseInt(width);
            }, 500);
            request(downloadApi)
              .pipe(stream)
              .on("close", function () {
                cb();
              })
              .on("error", function (err) {
                cb(err);
              });
          });
        },

        update: function () {
          var self = this;
          // self.loading2 = true;
          // self.loadingText = "版本下载中..."
          var downloadApi = this.domainName + "/pcUpdate/cashierPc/updapp.zip";
          this.downloadNewVersion(
            downloadApi,
            "./app",
            function (err, filePath) {
              window.clearInterval(self.timer);
              if (err) {
                self.$message({
                  message: "下载更新失败",
                  type: "error",
                  duration: 1500,
                });
                return;
              }
              self.updater.unpackNewVersion("./app", function () {
                self.progress = false;
                self.loading2 = true;
                self.loadingText = "更新完成，5秒后将重启程序...";
                self.updater.cleanOldVersion(filePath, function (err) {});
                setTimeout(function () {
                  self.updater.restartApp(self.gui.App);
                  self.loading2 = false;
                }, 5000);
              });
            }
          );
        },

        checkUpdate() {
          var self = this;

          self.gui = require("nw.gui");
          var path = require("path");

          self.updater = require("hupdater");
          var pkg = require("../package");
          // var pkg2 = require('.././package');

          //更新流程
          var localVersion = pkg.version; //版本号
          self.version = localVersion;
          // self.serverUrl = pkg.updateServer; //更新服务地址

          var targetPath = path.dirname(process.execPath); //本地程序根目录

          //检测更新
          self.versionCheckApi =
            self.domainName + "/pcUpdate/cashierPc/package.json";
          //开始检查版本号
          self.updater.checkVersion(
            self.versionCheckApi,
            function (err, newVersion) {
              //判断是否需要更新
              var ifUpdate = self.updater.ifNeedUpdate(
                localVersion,
                newVersion.version
              );
              if (ifUpdate) {
                self.centerDialogVisible = ifUpdate;
                self.desc = newVersion.desc;
                self.newVersion = newVersion.version;
              }
            }
          );
        },
      },
    });
  </script>
</html>
